import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt

# 1. Fetch option chain for the nearest expiration
ticker = 'AAPL'
stock = yf.Ticker(ticker)
exp_date = stock.options[0]  # nearest expiration
chain = stock.option_chain(exp_date)

# 2. Compute premium spent: volume * lastPrice * contract size (100)
calls = chain.calls.copy()
puts  = chain.puts.copy()
calls['premium_spent'] = calls['volume'] * calls['lastPrice'] * 100
puts['premium_spent']  = puts['volume']  * puts['lastPrice']  * 100

# 3. Merge calls & puts on strike, fill missing with zero
df = pd.merge(calls[['strike','premium_spent']],
              puts[['strike','premium_spent']],
              on='strike', how='outer',
              suffixes=('_call','_put')).fillna(0)

# 4. Compute net premium (call minus put)
df['net_premium'] = df['premium_spent_call'] - df['premium_spent_put']

# 5. Sort by strike and set up colors
df = df.sort_values('strike')
colors = df['net_premium'].apply(lambda x: 'green' if x>0 else 'red')

# 6. Plot horizontal bar chart
plt.figure(figsize=(8,6))
plt.barh(df['strike'].astype(str), df['net_premium']/1e6, color=colors)
plt.axvline(0, color='black', linewidth=0.8)

# 7. Formattingplt.xlabel('Net Premium (Millions USD)')
plt.ylabel('Strike')
plt.title(f'{ticker} Net Premium by Strike ({exp_date})')
plt.grid(axis='x', linestyle='--', alpha=0.5)

# 8. Flip the y-axis so highest strikes on top
plt.gca().invert_yaxis()

# 9. Show plot
plt.tight_layout()
plt.show()